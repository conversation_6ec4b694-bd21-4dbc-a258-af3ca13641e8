package com.ctrip.dcs.ops.infrastructure.util;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * 问题订单Excel导出数据模型 - 英文版
 */
@Data
@HeadStyle(fillForegroundColor = 22, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderProblemExcelDataEN {

    @ExcelProperty(value = "Order ID", index = 0)
    @ColumnWidth(15)
    private String orderId;

    @ExcelProperty(value = "Use Date", index = 1)
    @ColumnWidth(12)
    private String useDate;

    @ExcelProperty(value = "Driver ID", index = 2)
    @ColumnWidth(15)
    private String driverId;

    @ExcelProperty(value = "Driver Name", index = 3)
    @ColumnWidth(15)
    private String driverName;

    @ExcelProperty(value = "City Name", index = 4)
    @ColumnWidth(15)
    private String cityName;

    @ExcelProperty(value = "Defect Type", index = 5)
    @ColumnWidth(15)
    private String defectType;

    @ExcelProperty(value = "City ID", index = 6)
    @ColumnWidth(10)
    private Long cityId;

    public static OrderProblemExcelDataEN fromOrderProblemResultDTO(com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO dto) {
        OrderProblemExcelDataEN data = new OrderProblemExcelDataEN();
        data.setOrderId(dto.getOrderId());
        data.setUseDate(dto.getUseDate());
        data.setDriverId(dto.getDriverId());
        data.setDriverName(dto.getDriverName());
        data.setCityName(dto.getCityName());
        data.setDefectType(dto.getDefectType());
        data.setCityId(dto.getCityId());
        return data;
    }
} 