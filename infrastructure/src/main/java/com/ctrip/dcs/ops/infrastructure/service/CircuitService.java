package com.ctrip.dcs.ops.infrastructure.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.ops.infrastructure.config.CircuitTypeConfig;
import com.ctrip.dcs.ops.infrastructure.constant.OpsConstants;
import com.ctrip.dcs.ops.infrastructure.gateway.CityGateway;
import com.ctrip.dcs.ops.infrastructure.gateway.NepheleGateway;
import com.ctrip.dcs.ops.infrastructure.util.ExcelUtil;
import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import com.ctrip.dcs.ops.infrastructure.util.SharkUtil;
import com.ctrip.dcs.ops.infrastructure.value.*;

@Service
public class CircuitService extends BaseQuery {

    @Autowired
    private CityGateway cityGateway;

    @Autowired
    private NepheleGateway nepheleGateway;

    @Autowired
    private CircuitTypeConfig circuitTypeConfig;

    private Map<String, String> sortMap = ImmutableMap.of("ascend", "asc", "descend", "desc");

    private Map<String, String> sortField = ImmutableMap.of("defectCnt", "standard_notin_line_cnt");
    private Map<String, String> orderSortField = ImmutableMap.of("useDate", "use_day_local");

    /**
     * 通用参数构建器
     */
    private Map<String, Object> buildBaseParams(String partyId, String period, Long cityId, String serviceType) {
        serviceType = circuitTypeConfig.getServiceTypeMap().getOrDefault(serviceType, serviceType);
        String minDate = queryCircuitMinDate(period);
        Map<String, Object> param = new HashMap<>();
        param.put("hiveD", minDate);
        param.put("corpIdUse", Long.valueOf(partyId));
        param.put("useCityId", cityId);
        param.put("serviceType", serviceType);
        return param;
    }

    /**
     * 通用数据查询方法
     */
    private <T> T queryDataWithEmptyCheck(String apiName, Map<String, Object> params, T defaultResult, Function<List<Map<String, Object>>, T> processor) {
        List<Map<String, Object>> queryResult = daasGatewayV2.queryDataByDaas(apiName, params);
        if (CollectionUtils.isEmpty(queryResult)) {
            return defaultResult;
        }
        return processor.apply(queryResult);
    }

    /**
     * 通用分页信息构建
     */
    private PageDTO buildPageInfo(Integer total, Integer size) {
        PageDTO pageDTO = new PageDTO();
        pageDTO.setTotalSize(total);
        pageDTO.setTotalPages((total / size) + ((total % size) == 0 ? 0 : 1));
        return pageDTO;
    }



    /**
     * DTO映射方法 - CircuitOverviewDTO
     */
    private CircuitOverviewDTO mapToCircuitOverviewDTO(Map<String, Object> dataMap) {
        CircuitOverviewDTO dto = new CircuitOverviewDTO();
        dto.setOrderCnt(MapUtil.getKeyAsLong(dataMap, "ven_taken_cnt"));
        dto.setOrderCompletionCnt(MapUtil.getKeyAsLong(dataMap, "ven_complete_cnt"));
        dto.setNoVehicleCnt(MapUtil.getKeyAsLong(dataMap, "indemn_source_after_appe_cnt"));
        dto.setVehicleAndPersonNotMatchCnt(MapUtil.getKeyAsLong(dataMap, "people_vehi_no_match_cnt"));
        dto.setNotStandardizedCNt(MapUtil.getKeyAsLong(dataMap, "standard_notin_line_cnt"));
        dto.setOtherCnt(MapUtil.getKeyAsLong(dataMap, "nocarbf_xnota_cnt"));
        dto.setNoVehicleRatio(MathUtil.serDoubleAccuracy(BigDecimal.valueOf(MapUtil.getKeyAsDouble(dataMap, "nocar_after_rate")).multiply(BigDecimal.valueOf(100)).doubleValue(), 2));
        dto.setVehicleAndPersonNotMatchRatio(MathUtil.serDoubleAccuracy(BigDecimal.valueOf(MapUtil.getKeyAsDouble(dataMap, "people_vehi_no_match_rate")).multiply(BigDecimal.valueOf(100)).doubleValue(), 2));
        dto.setNotStandardizedRatio(MathUtil.serDoubleAccuracy(BigDecimal.valueOf(MapUtil.getKeyAsDouble(dataMap, "standard_in_line_rate")).multiply(BigDecimal.valueOf(100)).doubleValue(), 2));
        dto.setNoVehicleIsCircuited(MapUtil.getKeyAsInteger(dataMap, "if_bk_nocar_after"));
        dto.setVehicleAndPersonNotMatchIsCircuited(MapUtil.getKeyAsInteger(dataMap, "if_bk_people_vehi_no_match"));
        dto.setNotStandardizedIsCircuited(MapUtil.getKeyAsInteger(dataMap, "if_bk_standard_in_line"));
        dto.setNoVehicleisWarn(MapUtil.getKeyAsInteger(dataMap, "if_alarm_nocar_after"));
        dto.setVehicleAndPersonNotMatchisWarn(MapUtil.getKeyAsInteger(dataMap, "if_alarm_people_vehi_no_match"));
        dto.setNotStandardizedisWarn(MapUtil.getKeyAsInteger(dataMap, "if_alarm_standard_in_line"));
        return dto;
    }

    public CircuitOverviewDTO queryCircuitOverviewData(String partyId, String period, Long cityId, String serviceType) {
        Map<String, Object> params = buildBaseParams(partyId, period, cityId, serviceType);

        return queryDataWithEmptyCheck("queryCircuitOverviewData", params, new CircuitOverviewDTO(),
            queryResult -> mapToCircuitOverviewDTO(queryResult.get(0)));
    }

    /**
     * 处理问题概览数据的业务逻辑
     */
    private List<ProblemOverviewDTO> processProblemOverviewData(List<Map<String, Object>> queryResult, String locale) {
        Map<String, Integer> collect = queryResult.stream()
            .collect(Collectors.toMap(
                item -> MapUtil.getKeyAsString(item, "type"),
                item -> MapUtil.getKeyAsInteger(item, "cnt"),
                (k1, k2) -> k2));

        int total = collect.values().stream().mapToInt(Integer::intValue).sum();

        List<ProblemOverviewDTO> result = new ArrayList<>();
        circuitTypeConfig.getProblemTypeMap().forEach((key, value) -> {
            ProblemOverviewDTO dto = new ProblemOverviewDTO();
            dto.setProblemName(SharkUtil.get(OpsConstants.generateCircuitShark(value), locale, value));
            dto.setProblemType(value);
            Integer cnt = collect.getOrDefault(key, 0);
            dto.setCnt(cnt);
            BigDecimal ratio = total > 0 ?
                BigDecimal.valueOf(cnt).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
            dto.setRatio(MathUtil.serDoubleAccuracy(ratio.doubleValue(), 2));
            result.add(dto);
        });
        
        // 按照cnt从大到小排序
        result.sort((a, b) -> Integer.compare(b.getCnt(), a.getCnt()));
        
        return result;
    }

    public List<ProblemOverviewDTO> queryProblemOverviewData(String partyId, String period, Long cityId, String serviceType, String locale) {
        Map<String, Object> params = buildBaseParams(partyId, period, cityId, serviceType);

        return queryDataWithEmptyCheck("queryProblemOverviewData", params, new ArrayList<>(),
            queryResult -> processProblemOverviewData(queryResult, locale));
    }

    /**
     * 通用分页查询方法
     */
    private <T, R> R executePagedQuery(String apiName, Map<String, Object> baseParams,
                                      Integer pageNo, Integer size, R defaultResult,
                                      Function<List<Map<String, Object>>, List<T>> dataMapper,
                                      BiFunction<List<T>, PageDTO, R> resultBuilder) {
        // 构建分页参数
        Map<String, Object> params = new HashMap<>(baseParams);
        params.put("offsetCnt", (pageNo - 1) * size);
        params.put("limitCnt", size);
        params.put("countTotal", true);

        // 查询总数
        List<Map<String, Object>> countResult = daasGatewayV2.queryDataByDaas(apiName, params);
        if (CollectionUtils.isEmpty(countResult)) {
            return defaultResult;
        }

        Integer total = MapUtil.getKeyAsInteger(countResult.get(0), "cnt");
        PageDTO pageDTO = buildPageInfo(total, size);

        // 查询数据
        params.put("countTotal", false);
        List<Map<String, Object>> dataResult = daasGatewayV2.queryDataByDaas(apiName, params);

        List<T> resultList = CollectionUtils.isEmpty(dataResult) ?
            new ArrayList<>() : dataMapper.apply(dataResult);

        return resultBuilder.apply(resultList, pageDTO);
    }

    /**
     * DTO映射方法 - OrderProblemResultDTO
     */
    private OrderProblemResultDTO mapToOrderProblemResultDTO(Map<String, Object> dataMap, String locale) {
        OrderProblemResultDTO dto = new OrderProblemResultDTO();
        dto.setId(MapUtil.getKeyAsLong(dataMap, "id"));
        dto.setOrderId(MapUtil.getKeyAsString(dataMap, "order_id"));
        dto.setUseDate(MapUtil.getKeyAsString(dataMap, "use_day_local"));
        dto.setDriverId(MapUtil.getKeyAsString(dataMap, "driver_id_last"));
        dto.setDriverName(MapUtil.getKeyAsString(dataMap, "driver_name_last"));
        dto.setDefectType(SharkUtil.get(OpsConstants.generateCircuitShark(circuitTypeConfig.getOrderProblemTypeMap().get(MapUtil.getKeyAsString(dataMap, "type"))), locale, MapUtil.getKeyAsString(dataMap, "type")));
        dto.setCityId(MapUtil.getKeyAsLong(dataMap, "use_city_id"));
        return dto;
    }

    /**
     * 为订单问题结果列表添加城市名称
     */
    private void enrichOrderProblemWithCityNames(List<OrderProblemResultDTO> resultList, String locale) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<Long> cityIdList = resultList.stream()
            .map(OrderProblemResultDTO::getCityId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(cityIdList)) {
            Map<Long, City> cityMap = cityGateway.getCityName(cityIdList, locale);
            resultList.forEach(dto -> {
                Long cityId = dto.getCityId();
                if (cityId != null && cityMap.containsKey(cityId)) {
                    City city = cityMap.get(cityId);
                    dto.setCityName(city.getTranslationName());
                }
            });
        }
    }

    public OrderProblemPageDTO queryOrderProblemList(String partyId, String period, Long cityId, String serviceType, String type, String locale, Integer pageNo, Integer size, String field, String sortOrder) {
        Map<String, Object> baseParams = buildBaseParams(partyId, period, cityId, serviceType);
        if (StringUtils.isNotBlank(type)) {
            baseParams.put("type", circuitTypeConfig.getReverseProblemTypeMap().get(type));
        }
        baseParams.put("orderField", orderSortField.getOrDefault(field, "use_day_local"));
        baseParams.put("sortOrder", sortMap.getOrDefault(sortOrder, "desc"));
        return executePagedQuery("queryOrderProblemList", baseParams, pageNo, size,
                new OrderProblemPageDTO(),
                dataList -> dataList.stream().map(item -> mapToOrderProblemResultDTO(item, locale)).collect(Collectors.toList()),
                (resultList, pageDTO) -> {
                    enrichOrderProblemWithCityNames(resultList, locale);
                    OrderProblemPageDTO result = new OrderProblemPageDTO();
                    result.setOrderProblemResultDTOS(resultList);
                    result.setPageDTO(pageDTO);
                    return result;
                });
    }

    /**
     * DTO映射方法 - DriverProblemResultDTO
     */
    private DriverProblemResultDTO mapToDriverProblemResultDTO(Map<String, Object> dataMap) {
        DriverProblemResultDTO dto = new DriverProblemResultDTO();
        dto.setDriverId(MapUtil.getKeyAsString(dataMap, "driver_id_last"));
        dto.setDriverName(MapUtil.getKeyAsString(dataMap, "driver_name_last"));
        dto.setCityId(MapUtil.getKeyAsLong(dataMap, "use_city_id"));
        dto.setDefectCnt(MapUtil.getKeyAsString(dataMap, "standard_notin_line_cnt"));
        return dto;
    }

    /**
     * 为司机问题结果列表添加城市名称
     */
    private void enrichDriverProblemWithCityNames(List<DriverProblemResultDTO> resultList, String locale) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<Long> cityIdList = resultList.stream()
            .map(DriverProblemResultDTO::getCityId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(cityIdList)) {
            Map<Long, City> cityMap = cityGateway.getCityName(cityIdList, locale);
            resultList.forEach(dto -> {
                Long cityId = dto.getCityId();
                if (cityId != null && cityMap.containsKey(cityId)) {
                    City city = cityMap.get(cityId);
                    dto.setCityName(city.getTranslationName());
                }
            });
        }
    }

    public DriverProblemPageDTO queryDriverProblemList(String partyId, String period, Long cityId, String serviceType, String locale, Integer pageNo, Integer size, String field, String sortOrder) {
        Map<String, Object> baseParams = buildBaseParams(partyId, period, cityId, serviceType);
        baseParams.put("orderField", sortField.getOrDefault(field, "standard_notin_line_cnt"));
        baseParams.put("sortOrder", sortMap.getOrDefault(sortOrder, "desc"));

        return executePagedQuery("queryDrvProblemList", baseParams, pageNo, size,
            new DriverProblemPageDTO(),
            dataList -> dataList.stream().map(this::mapToDriverProblemResultDTO).collect(Collectors.toList()),
            (resultList, pageDTO) -> {
                enrichDriverProblemWithCityNames(resultList, locale);
                DriverProblemPageDTO result = new DriverProblemPageDTO();
                result.setDriverProblemResultDTOS(resultList);
                result.setPageDTO(pageDTO);
                return result;
            });
    }
    /**
     * 使用ID游标查询订单问题数据 - 专门用于导出场景的高效查询
     *
     * @param partyId 供应商ID
     * @param period 查询周期
     * @param cityId 城市ID
     * @param serviceType 服务类型
     * @param type 问题类型
     * @param locale 语言环境
     * @param lastId 上次查询的最后一个订单ID，为null时从头开始查询
     * @param limitSize 查询数量限制
     * @return 订单问题结果列表
     */
    private List<OrderProblemResultDTO> queryOrderProblemListByIdCursor(String partyId, String period, Long cityId, String serviceType, String type, String locale, Long lastId, Integer limitSize) {
        Map<String, Object> baseParams = buildBaseParams(partyId, period, cityId, serviceType);
        if (StringUtils.isNotBlank(type)) {
            baseParams.put("type", circuitTypeConfig.getReverseProblemTypeMap().get(type));
        }
        
        // 使用订单ID排序，确保游标查询的一致性
        baseParams.put("orderField", "id");
        baseParams.put("sortOrder", "asc");

        // 构建ID游标查询参数
        Map<String, Object> params = new HashMap<>(baseParams);
        if (Objects.nonNull(lastId)) {
            // 如果有游标ID，则查询大于该ID的记录
            params.put("lastId", lastId);
        } else {
            params.put("lastId", 0L);
        }
        params.put("limitCnt", limitSize);

        // 直接查询数据
        List<Map<String, Object>> dataResult = daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", params);

        if (CollectionUtils.isEmpty(dataResult)) {
            return new ArrayList<>();
        }

        // 转换为DTO并添加城市名称
        List<OrderProblemResultDTO> resultList = dataResult.stream()
            .map(item -> mapToOrderProblemResultDTO(item, locale))
            .collect(Collectors.toList());

        enrichOrderProblemWithCityNames(resultList, locale);

        return resultList;
    }

    /**
     * 兼容原有接口的方法
     */
    public String uploadOrderProblemList(String partyId, String period, Long cityId, String serviceType, String locale) {
        // 默认查询所有类型的问题订单
        return uploadOrderProblemList(partyId, period, cityId, serviceType, null, locale);
    }

    /**
     * 流式分页查询问题订单并导出Excel - 避免一次性加载所有数据到内存
     *
     * @param partyId 供应商ID
     * @param period 查询周期
     * @param cityId 城市ID
     * @param serviceType 服务类型
     * @param type 问题类型
     * @param locale 语言环境
     * @return Excel文件下载链接
     */
    public String uploadOrderProblemList(String partyId, String period, Long cityId, String serviceType, String type, String locale) {
        try {
            // 使用流式Excel生成，分批查询并写入
            byte[] excelBytes = ExcelUtil.generateOrderProblemExcelStream(writer -> {
                streamQueryOrderProblems(partyId, period, cityId, serviceType, type, locale, writer);
            }, locale);

            // 生成文件名
            String fileName = ExcelUtil.generateFileName(partyId, period, cityId, serviceType, type);

            // 上传到NepheleGateway并返回下载链接
            return nepheleGateway.upload(excelBytes, fileName);

        } catch (Exception e) {
            throw new RuntimeException("导出问题订单Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式ID游标查询问题订单数据并写入Excel - 使用id+limit方式避免深度分页问题
     *
     * @param partyId 供应商ID
     * @param period 查询周期
     * @param cityId 城市ID
     * @param serviceType 服务类型
     * @param type 问题类型
     * @param locale 语言环境
     * @param writer Excel数据写入器
     */
    private void streamQueryOrderProblems(String partyId, String period, Long cityId,
                                         String serviceType, String type, String locale,
                                         ExcelUtil.StreamExcelDataWriter writer) {
        Long lastId = 0L; // 游标ID，用于追踪查询位置
        int limitSize = 100; // 每次查询100条记录
        boolean hasData = false;

        while (true) {
            try {
                // 使用ID游标查询，避免深度分页
                List<OrderProblemResultDTO> batchResult = queryOrderProblemListByIdCursor(partyId, period, cityId, serviceType, type, locale, lastId, limitSize);

                if (!CollectionUtils.isEmpty(batchResult)) {
                    hasData = true;
                    // 立即写入Excel，不在内存中累积
                    writer.writeDataBatch(batchResult);
                    
                    // 检查退出条件：数据量小于limit，说明已经是最后一批数据
                    if (batchResult.size() < limitSize) {
                        break;
                    }
                    
                    // 更新游标为最后一条记录的orderId
                    lastId = batchResult.get(batchResult.size() - 1).getId();
                } else {
                    // 查询不到数据，退出循环
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException("ID游标查询问题订单数据失败，lastOrderId: " + lastId + ", 错误: " + e.getMessage(), e);
            }
        }

        if (!hasData) {
            throw new RuntimeException("没有查询到问题订单数据");
        }
    }

}
