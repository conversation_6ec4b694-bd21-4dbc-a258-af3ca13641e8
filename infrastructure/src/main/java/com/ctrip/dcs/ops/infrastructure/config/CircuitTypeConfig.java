package com.ctrip.dcs.ops.infrastructure.config;

import lombok.Data;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Map;

@Component
@Data
public class CircuitTypeConfig {
    
    @QMapConfig(value = "circuit.type.properties", key = "problemTypeMap")
    private Map<String, String> problemTypeMap;
    
    @QMapConfig(value = "circuit.type.properties", key = "orderProblemTypeMap")
    private Map<String, String> orderProblemTypeMap;
    
    @QMapConfig(value = "circuit.type.properties", key = "reverseProblemTypeMap")
    private Map<String, String> reverseProblemTypeMap;
    
    @QMapConfig(value = "circuit.type.properties", key = "serviceTypeMap")
    private Map<String, String> serviceTypeMap;
}